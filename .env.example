# Application Settings
ENV=
APP_NAME=mcp-service
DEBUG=false
PORT=50058

# Database Settings
DB_HOST=localhost
DB_PORT=5432
DB_USER=user_service
DB_PASSWORD=userpass
DB_NAME=user_db

# Redis Settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Proto
REPO_URL=
GIT_TOKEN=

# GCS Creds
GCS_CRED=
BUCKET_NAME=

FRONTEND_URL=
BOOTSTRAP_SERVERS=

GOOGLE_APPLICATION_CREDENTIALS_BASE64=
GOOGLE_CLOUD_PROJECT_ID=
GOOGLE_SERVICE_NAME=
REGION=
TOPIC_NAME=


DEFAULT_SSH_HOST=
DEFAULT_SSH_USER=
DEFAULT_SSH_KEY_CONTENT=

# Google secret manager 
GOOGLE_APPLICATION_CREDENTIALS=
GOOGLE_PROJECT_ID=
