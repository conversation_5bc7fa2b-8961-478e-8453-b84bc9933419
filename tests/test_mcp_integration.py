#!/usr/bin/env python3
"""
Integration test for MCP service with updated schema.
This test verifies the complete flow from API Gateway to MCP Service.
"""

import asyncio
import json
from unittest.mock import AsyncMock, patch
import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.schemas.mcp import MCPCreate, McpCategory, VisibilityEnum, StatusEnum, UrlTypeEnum, McpComponentCategory

client = TestClient(app)

# Test data with new fields
test_mcp_data = {
    "name": "Integration Test MCP",
    "description": "Testing MCP creation with new schema fields",
    "git_url": "https://github.com/test/integration-test-mcp.git",
    "git_branch": "main",
    "category": "engineering",
    "visibility": "private",
    "status": "active",
    "mcp_type": "stdio",
    "component_category": "database",
    "tags": ["test", "integration", "database"],
    "user_ids": [],
    # New fields
    "repo_name": "integration-test-mcp",
    "git_user_name": "integrationtester",
    "integrations": ["postgresql", "redis", "elasticsearch"],
}

def test_mcp_schema_creation():
    """Test that MCPCreate schema works with new fields."""
    try:
        mcp_create = MCPCreate(**test_mcp_data)
        
        # Verify new fields are present
        assert mcp_create.repo_name == "integration-test-mcp"
        assert mcp_create.git_user_name == "integrationtester"
        assert mcp_create.integrations == ["postgresql", "redis", "elasticsearch"]
        
        # Verify old fields are not present
        assert not hasattr(mcp_create, 'env_keys')
        assert not hasattr(mcp_create, 'oauth_details')
        
        print("✅ MCP schema creation test passed")
        return True
    except Exception as e:
        print(f"❌ MCP schema creation test failed: {e}")
        return False

@patch('app.services.mcp_service.MCPServiceClient.createMCP')
@patch('app.services.user_service.UserServiceClient.get_user_details')
def test_mcp_api_endpoint(mock_get_user, mock_create_mcp):
    """Test the MCP creation API endpoint with new fields."""
    try:
        # Mock user details
        mock_get_user.return_value = AsyncMock(return_value={
            "success": True,
            "user": {
                "id": "test-user-id",
                "name": "Test User",
                "email": "<EMAIL>"
            }
        })
        
        # Mock MCP creation response
        mock_create_mcp.return_value = AsyncMock(return_value=type('MockResponse', (), {
            'success': True,
            'message': 'MCP created successfully',
            'mcp': type('MockMCP', (), {
                'id': 'test-mcp-id',
                'name': 'Integration Test MCP',
                'repo_name': 'integration-test-mcp',
                'git_user_name': 'integrationtester',
                'integrations': ['postgresql', 'redis', 'elasticsearch']
            })()
        })())
        
        # Mock authentication
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.return_value = lambda func: func
            
            # Make API request
            response = client.post(
                "/api/v1/mcps/",
                json=test_mcp_data,
                headers={"Authorization": "Bearer test-token"}
            )
        
        # Verify the API call was made with correct parameters
        assert mock_create_mcp.called
        call_args = mock_create_mcp.call_args
        
        # Check that new fields were passed to the service
        assert 'repo_name' in call_args.kwargs
        assert 'git_user_name' in call_args.kwargs
        assert 'integrations' in call_args.kwargs
        
        # Check that old fields were not passed
        assert 'env_keys' not in call_args.kwargs
        assert 'oauth_details' not in call_args.kwargs
        
        print("✅ MCP API endpoint test passed")
        return True
    except Exception as e:
        print(f"❌ MCP API endpoint test failed: {e}")
        return False

def test_mcp_service_client_signature():
    """Test that MCP service client has the correct method signature."""
    try:
        from app.services.mcp_service import MCPServiceClient
        import inspect
        
        # Get the createMCP method signature
        create_mcp_method = getattr(MCPServiceClient, 'createMCP')
        signature = inspect.signature(create_mcp_method)
        
        # Check that new parameters are present
        assert 'repo_name' in signature.parameters
        assert 'git_user_name' in signature.parameters
        assert 'integrations' in signature.parameters
        
        # Check that old parameters are not present
        assert 'env_keys' not in signature.parameters
        assert 'oauth_details' not in signature.parameters
        
        print("✅ MCP service client signature test passed")
        return True
    except Exception as e:
        print(f"❌ MCP service client signature test failed: {e}")
        return False

def test_removed_api_endpoints():
    """Test that env-related API endpoints have been removed."""
    try:
        # Test that env-update endpoint returns 404
        response = client.put("/api/v1/mcps/env-update?mcp_id=test", json={"env_key_values": []})
        assert response.status_code == 404
        
        # Test that get-env-details endpoint returns 404
        response = client.get("/api/v1/mcps/get-env-details/test-mcp-id")
        assert response.status_code == 404
        
        print("✅ Removed API endpoints test passed")
        return True
    except Exception as e:
        print(f"❌ Removed API endpoints test failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🧪 Running MCP Integration Tests")
    print("=" * 50)
    
    tests = [
        ("MCP Schema Creation", test_mcp_schema_creation),
        ("MCP API Endpoint", test_mcp_api_endpoint),
        ("MCP Service Client Signature", test_mcp_service_client_signature),
        ("Removed API Endpoints", test_removed_api_endpoints),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Integration Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed!")
        print("✅ MCP service updates are working correctly")
        return True
    else:
        print("⚠️  Some integration tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
