import pytest
import uuid
import json
import grpc # Add this import
from sqlalchemy.orm import Session
from unittest.mock import Magic<PERSON>ock
from datetime import datetime, timezone

from app.models.mcp_schema import McpConfig, UserMcpAssignment
from app.services.mcp_functions import MCPFunctionService
from app.grpc import mcp_pb2
from app.db.session import SessionLocal

# Helper to create a mock gRPC context
def mock_grpc_context():
    context = MagicMock(spec=grpc.ServicerContext)
    context.set_code = MagicMock()
    context.set_details = MagicMock()
    return context

@pytest.fixture(scope="function")
def db_session():
    """Provides a database session for a test."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.rollback() # Ensure no changes persist
        db.close()

@pytest.fixture(scope="function")
def mcp_function_service():
    """Provides an instance of MCPFunctionService."""
    return MCPFunctionService()

def test_update_tool_output_schema_success(db_session: Session, mcp_function_service: MCPFunctionService):
    # 1. Setup: Create an McpConfig with initial tools config
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4()) # Define owner_id
    tool_name_to_update = "test_tool_1"
    initial_tools_config = {
        "tools": [
            {"name": tool_name_to_update, "description": "A test tool", "input_schema": {}, "output_schema": None},
            {"name": "test_tool_2", "description": "Another tool", "input_schema": {}, "output_schema": None},
        ]
    }
    mcp_config = McpConfig(
        id=mcp_id,
        name="Test MCP for Schema Update",
        owner_id=owner_id, # Use defined owner_id
        mcp_tools_config=initial_tools_config,
        deployment_status="completed",
        visibility="private",
        status="active",
        department="engineering", # Ensure all required fields are present
        owner_type="user",      # Ensure all required fields are present
        # Add other required fields for McpConfig if any, e.g., created_at, updated_at if not auto-set
    )
    db_session.add(mcp_config)
    db_session.commit()
    db_session.refresh(mcp_config)

    # 2. Prepare request
    new_output_schema = {"type": "object", "properties": {"result": {"type": "string"}}}
    request = mcp_pb2.UpdateToolOutputSchemaRequest(
        mcp_id=mcp_id,
        tool_name=tool_name_to_update,
        output_schema_json=json.dumps(new_output_schema)
    )
    context = mock_grpc_context()

    # 3. Execute
    response = mcp_function_service.UpdateToolOutputSchema(request, context)

    # 4. Assert Response
    assert response.success is True
    assert response.message == f"Output schema for tool '{tool_name_to_update}' updated successfully."
    assert response.mcp.id == mcp_id
    
    # 5. Assert Database state
    updated_mcp_config = db_session.query(McpConfig).filter(McpConfig.id == mcp_id).first()
    assert updated_mcp_config is not None
    
    # Ensure mcp_tools_config is treated as a dictionary
    if isinstance(updated_mcp_config.mcp_tools_config, str):
        updated_tools_config = json.loads(updated_mcp_config.mcp_tools_config)
    else:
        updated_tools_config = updated_mcp_config.mcp_tools_config
        
    assert updated_tools_config is not None
    
    found_updated_tool = False
    for tool in updated_tools_config.get("tools", []):
        if tool.get("name") == tool_name_to_update:
            assert tool.get("output_schema") == new_output_schema
            found_updated_tool = True
            break
    assert found_updated_tool is True

    # Clean up (handled by fixture's rollback)

def test_update_tool_output_schema_mcp_not_found(db_session: Session, mcp_function_service: MCPFunctionService):
    non_existent_mcp_id = str(uuid.uuid4())
    request = mcp_pb2.UpdateToolOutputSchemaRequest(
        mcp_id=non_existent_mcp_id,
        tool_name="any_tool",
        output_schema_json="{}"
    )
    context = mock_grpc_context()

    response = mcp_function_service.UpdateToolOutputSchema(request, context)

    assert response.success is False
    assert response.message == "MCP not found."
    context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)
    context.set_details.assert_called_with(f"MCP with ID {non_existent_mcp_id} not found.")

def test_update_tool_output_schema_tool_not_found(db_session: Session, mcp_function_service: MCPFunctionService):
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    initial_tools_config = {
        "tools": [
            {"name": "existing_tool", "description": "A tool", "input_schema": {}, "output_schema": None},
        ]
    }
    mcp_config = McpConfig(
        id=mcp_id, name="MCP For Tool Not Found Test", owner_id=owner_id,
        mcp_tools_config=initial_tools_config, deployment_status="completed",
        visibility="private", status="active", department="engineering", owner_type="user"
    )
    db_session.add(mcp_config)
    db_session.commit()

    request = mcp_pb2.UpdateToolOutputSchemaRequest(
        mcp_id=mcp_id,
        tool_name="non_existent_tool",
        output_schema_json=json.dumps({"type": "string"})
    )
    context = mock_grpc_context()
    response = mcp_function_service.UpdateToolOutputSchema(request, context)

    assert response.success is False
    assert response.message == "Tool 'non_existent_tool' not found."
    context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)
    context.set_details.assert_called_with("Tool 'non_existent_tool' not found in this MCP.")

def test_update_tool_output_schema_invalid_json(db_session: Session, mcp_function_service: MCPFunctionService):
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    tool_name = "tool_for_invalid_json"
    initial_tools_config = {
        "tools": [
            {"name": tool_name, "description": "A tool", "input_schema": {}, "output_schema": None},
        ]
    }
    mcp_config = McpConfig(
        id=mcp_id, name="MCP For Invalid JSON Test", owner_id=owner_id,
        mcp_tools_config=initial_tools_config, deployment_status="completed",
        visibility="private", status="active", department="engineering", owner_type="user"
    )
    db_session.add(mcp_config)
    db_session.commit()

    request = mcp_pb2.UpdateToolOutputSchemaRequest(
        mcp_id=mcp_id,
        tool_name=tool_name,
        output_schema_json="this is not valid json"
    )
    context = mock_grpc_context()
    response = mcp_function_service.UpdateToolOutputSchema(request, context)

    assert response.success is False
    assert response.message == "Invalid JSON format for output_schema."
    context.set_code.assert_called_with(grpc.StatusCode.INVALID_ARGUMENT)
    # The exact detail message might vary based on json.JSONDecodeError
    assert "Invalid JSON format for output_schema" in context.set_details.call_args[0][0]


def test_update_tool_output_schema_no_mcp_tools_config(db_session: Session, mcp_function_service: MCPFunctionService):
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    mcp_config = McpConfig(
        id=mcp_id, name="MCP No Tools Config", owner_id=owner_id,
        mcp_tools_config=None, # Explicitly None
        deployment_status="completed", visibility="private", status="active",
        department="engineering", owner_type="user"
    )
    db_session.add(mcp_config)
    db_session.commit()

    request = mcp_pb2.UpdateToolOutputSchemaRequest(
        mcp_id=mcp_id,
        tool_name="any_tool",
        output_schema_json=json.dumps({"type": "string"})
    )
    context = mock_grpc_context()
    response = mcp_function_service.UpdateToolOutputSchema(request, context)

    assert response.success is False
    assert response.message == "MCP tools configuration not found or malformed." # Corrected based on code
    context.set_code.assert_called_with(grpc.StatusCode.FAILED_PRECONDITION)
    context.set_details.assert_called_with("MCP tools configuration not found or malformed.") # Corrected based on code


def test_update_tool_output_schema_malformed_mcp_tools_config_no_tools_key(db_session: Session, mcp_function_service: MCPFunctionService):
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    initial_tools_config = {"meta": "some_meta_info"} # Missing "tools" key
    mcp_config = McpConfig(
        id=mcp_id, name="MCP Malformed Config No Tools Key", owner_id=owner_id,
        mcp_tools_config=initial_tools_config, deployment_status="completed",
        visibility="private", status="active", department="engineering", owner_type="user"
    )
    db_session.add(mcp_config)
    db_session.commit()

    request = mcp_pb2.UpdateToolOutputSchemaRequest(
        mcp_id=mcp_id,
        tool_name="any_tool",
        output_schema_json=json.dumps({"type": "string"})
    )
    context = mock_grpc_context()
    response = mcp_function_service.UpdateToolOutputSchema(request, context)

    assert response.success is False
    assert response.message == "MCP tools configuration not found or malformed." # Corrected based on code
    context.set_code.assert_called_with(grpc.StatusCode.FAILED_PRECONDITION)
    context.set_details.assert_called_with("MCP tools configuration not found or malformed.") # Corrected based on code


def test_update_tool_output_schema_malformed_mcp_tools_config_tools_not_list(db_session: Session, mcp_function_service: MCPFunctionService):
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    initial_tools_config = {"tools": "this should be a list"} # "tools" is not a list
    mcp_config = McpConfig(
        id=mcp_id, name="MCP Malformed Config Tools Not List", owner_id=owner_id,
        mcp_tools_config=initial_tools_config, deployment_status="completed",
        visibility="private", status="active", department="engineering", owner_type="user"
    )
    db_session.add(mcp_config)
    db_session.commit()

    request = mcp_pb2.UpdateToolOutputSchemaRequest(
        mcp_id=mcp_id,
        tool_name="any_tool",
        output_schema_json=json.dumps({"type": "string"})
    )
    context = mock_grpc_context()
    response = mcp_function_service.UpdateToolOutputSchema(request, context)

    assert response.success is False
    assert response.message == "MCP tools configuration is malformed."
    context.set_code.assert_called_with(grpc.StatusCode.FAILED_PRECONDITION)
    context.set_details.assert_called_with("MCP tools configuration is malformed.")


# ============================================================================
# DELETE MCP TESTS
# ============================================================================

def test_delete_mcp_owner_with_other_users_soft_delete(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test owner deleting MCP when other users have assignments - should soft delete."""
    # Setup: Create MCP and assignments
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    other_user_id = str(uuid.uuid4())

    # Create MCP
    mcp_config = McpConfig(
        id=mcp_id,
        name="Test MCP for Soft Delete",
        owner_id=owner_id,
        deployment_status="completed",
        visibility="private",
        status="active",
        category="engineering",
        owner_type="user"
    )
    db_session.add(mcp_config)

    # Create assignments for owner and other user
    owner_assignment = UserMcpAssignment(
        user_id=owner_id,
        mcp_id=mcp_id
    )
    other_assignment = UserMcpAssignment(
        user_id=other_user_id,
        mcp_id=mcp_id
    )
    db_session.add(owner_assignment)
    db_session.add(other_assignment)
    db_session.commit()

    # Execute: Owner deletes MCP
    request = mcp_pb2.DeleteMCPRequest(id=mcp_id, user_id=owner_id)
    context = mock_grpc_context()
    response = mcp_function_service.deleteMCP(request, context)

    # Assert: Should soft delete
    assert response.success is True
    assert "soft-deleted" in response.message.lower()
    assert "other users" in response.message.lower()

    # Verify MCP is soft deleted
    updated_mcp = db_session.query(McpConfig).filter(McpConfig.id == mcp_id).first()
    assert updated_mcp.deleted_at is not None


def test_delete_mcp_owner_no_other_users_hard_delete(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test owner deleting MCP when no other users have assignments - should hard delete."""
    # Setup: Create MCP with only owner assignment
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())

    # Create MCP
    mcp_config = McpConfig(
        id=mcp_id,
        name="Test MCP for Hard Delete",
        owner_id=owner_id,
        deployment_status="completed",
        visibility="private",
        status="active",
        category="engineering",
        owner_type="user"
    )
    db_session.add(mcp_config)

    # Create only owner assignment
    owner_assignment = UserMcpAssignment(
        user_id=owner_id,
        mcp_id=mcp_id
    )
    db_session.add(owner_assignment)
    db_session.commit()

    # Execute: Owner deletes MCP
    request = mcp_pb2.DeleteMCPRequest(id=mcp_id, user_id=owner_id)
    context = mock_grpc_context()
    response = mcp_function_service.deleteMCP(request, context)

    # Assert: Should hard delete
    assert response.success is True
    assert "deleted successfully" in response.message.lower()

    # Verify MCP is completely removed
    deleted_mcp = db_session.query(McpConfig).filter(McpConfig.id == mcp_id).first()
    assert deleted_mcp is None


def test_delete_mcp_already_soft_deleted_graceful_handling(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test owner trying to delete already soft-deleted MCP - should handle gracefully."""
    # Setup: Create already soft-deleted MCP
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())

    # Create MCP that's already soft deleted
    mcp_config = McpConfig(
        id=mcp_id,
        name="Test Already Deleted MCP",
        owner_id=owner_id,
        deployment_status="completed",
        visibility="private",
        status="active",
        category="engineering",
        owner_type="user",
        deleted_at=datetime.now(timezone.utc)
    )
    db_session.add(mcp_config)
    db_session.commit()

    # Execute: Owner tries to delete already deleted MCP
    request = mcp_pb2.DeleteMCPRequest(id=mcp_id, user_id=owner_id)
    context = mock_grpc_context()
    response = mcp_function_service.deleteMCP(request, context)

    # Assert: Should handle gracefully
    assert response.success is True
    assert "already soft deleted" in response.message.lower()


def test_delete_mcp_non_owner_removes_from_workspace(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test non-owner deleting MCP - should remove from their workspace."""
    # Setup: Create MCP and assignments
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())

    # Create MCP
    mcp_config = McpConfig(
        id=mcp_id,
        name="Test MCP for Workspace Removal",
        owner_id=owner_id,
        deployment_status="completed",
        visibility="private",
        status="active",
        category="engineering",
        owner_type="user"
    )
    db_session.add(mcp_config)

    # Create assignments
    owner_assignment = UserMcpAssignment(
        user_id=owner_id,
        mcp_id=mcp_id
    )
    user_assignment = UserMcpAssignment(
        user_id=user_id,
        mcp_id=mcp_id
    )
    db_session.add(owner_assignment)
    db_session.add(user_assignment)
    db_session.commit()

    # Execute: Non-owner removes MCP from workspace
    request = mcp_pb2.DeleteMCPRequest(id=mcp_id, user_id=user_id)
    context = mock_grpc_context()
    response = mcp_function_service.deleteMCP(request, context)

    # Assert: Should remove from workspace
    assert response.success is True
    assert "removed from your workspace" in response.message.lower()

    # Verify user assignment is removed
    user_assignment_after = db_session.query(UserMcpAssignment).filter(
        UserMcpAssignment.user_id == user_id,
        UserMcpAssignment.mcp_id == mcp_id
    ).first()
    assert user_assignment_after is None

    # Verify MCP still exists
    mcp_still_exists = db_session.query(McpConfig).filter(McpConfig.id == mcp_id).first()
    assert mcp_still_exists is not None
    assert mcp_still_exists.deleted_at is None


def test_delete_mcp_non_owner_assignment_not_found(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test non-owner trying to delete MCP they haven't added - should return error."""
    # Setup: Create MCP without user assignment
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())

    # Create MCP
    mcp_config = McpConfig(
        id=mcp_id,
        name="Test MCP for Assignment Not Found",
        owner_id=owner_id,
        deployment_status="completed",
        visibility="private",
        status="active",
        category="engineering",
        owner_type="user"
    )
    db_session.add(mcp_config)

    # Create only owner assignment (no user assignment)
    owner_assignment = UserMcpAssignment(
        user_id=owner_id,
        mcp_id=mcp_id
    )
    db_session.add(owner_assignment)
    db_session.commit()

    # Execute: Non-owner tries to remove MCP they haven't added
    request = mcp_pb2.DeleteMCPRequest(id=mcp_id, user_id=user_id)
    context = mock_grpc_context()
    response = mcp_function_service.deleteMCP(request, context)

    # Assert: Should return error
    assert response.success is False
    assert "assignment not found" in response.message.lower()
    context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)


def test_delete_mcp_not_found(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test deleting non-existent MCP - should return error."""
    # Setup: Use non-existent MCP ID
    non_existent_mcp_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())

    # Execute: Try to delete non-existent MCP
    request = mcp_pb2.DeleteMCPRequest(id=non_existent_mcp_id, user_id=user_id)
    context = mock_grpc_context()
    response = mcp_function_service.deleteMCP(request, context)

    # Assert: Should return error
    assert response.success is False
    assert "mcp not found" in response.message.lower()
    context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)


def test_delete_mcp_owner_only_assignment_hard_delete(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test owner deleting MCP when they are the only user - should hard delete."""
    # Setup: Create MCP with no assignments at all
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())

    # Create MCP without any assignments
    mcp_config = McpConfig(
        id=mcp_id,
        name="Test MCP for Owner Only Delete",
        owner_id=owner_id,
        deployment_status="completed",
        visibility="private",
        status="active",
        category="engineering",
        owner_type="user"
    )
    db_session.add(mcp_config)
    db_session.commit()

    # Execute: Owner deletes MCP
    request = mcp_pb2.DeleteMCPRequest(id=mcp_id, user_id=owner_id)
    context = mock_grpc_context()
    response = mcp_function_service.deleteMCP(request, context)

    # Assert: Should hard delete
    assert response.success is True
    assert "deleted successfully" in response.message.lower()

    # Verify MCP is completely removed
    deleted_mcp = db_session.query(McpConfig).filter(McpConfig.id == mcp_id).first()
    assert deleted_mcp is None


def test_delete_mcp_non_owner_from_soft_deleted_mcp(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test non-owner removing assignment from soft-deleted MCP - should work."""
    # Setup: Create soft-deleted MCP with user assignment
    mcp_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())

    # Create soft-deleted MCP
    mcp_config = McpConfig(
        id=mcp_id,
        name="Test Soft Deleted MCP",
        owner_id=owner_id,
        deployment_status="completed",
        visibility="private",
        status="active",
        category="engineering",
        owner_type="user",
        deleted_at=datetime.now(timezone.utc)
    )
    db_session.add(mcp_config)

    # Create user assignment
    user_assignment = UserMcpAssignment(
        user_id=user_id,
        mcp_id=mcp_id
    )
    db_session.add(user_assignment)
    db_session.commit()

    # Execute: Non-owner removes from workspace
    request = mcp_pb2.DeleteMCPRequest(id=mcp_id, user_id=user_id)
    context = mock_grpc_context()
    response = mcp_function_service.deleteMCP(request, context)

    # Assert: Should remove from workspace
    assert response.success is True
    assert "removed from your workspace" in response.message.lower()

    # Verify assignment is removed
    user_assignment_after = db_session.query(UserMcpAssignment).filter(
        UserMcpAssignment.user_id == user_id,
        UserMcpAssignment.mcp_id == mcp_id
    ).first()
    assert user_assignment_after is None