import json
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator

from app.schemas.agent import AgentCapabilitiesInDB, AgentInDB, CategoryEnum
from app.schemas.mcp import McpComponent<PERSON>ategory, MCPInDB
from app.schemas.workflow import WorkflowInDB


class MarketplaceItemSortEnum(str, Enum):
    NEWEST = "NEWEST"
    OLDEST = "OLDEST"
    MOST_POPULAR = "MOST_POPULAR"
    HIGHEST_RATED = "HIGHEST_RATED"


class PaginationMetadata(BaseModel):
    total: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_prev: bool
    next_page: Optional[int] = None
    prev_page: Optional[int] = None


class FileEntry(BaseModel):
    file: str
    created_at: str
    size: Optional[int] = None


class UrlEntry(BaseModel):
    url: str
    created_at: str


class MarketplaceAgentResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    avatar: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    use_count: Optional[int] = None
    visibility: str = "PUBLIC"
    workflow_ids: Optional[List[str]] = None
    mcp_server_ids: Optional[List[str]] = None
    agent_topic_type: Optional[str] = None
    is_a2a: Optional[bool] = None
    is_customizable: Optional[bool] = None
    capabilities: Optional[AgentCapabilitiesInDB] = Field(default=None, alias="agent_capabilities")
    example_prompts: Optional[List[str]] = None
    is_added: Optional[bool] = False
    category: Optional[CategoryEnum] = None
    # Additional fields from MarketplaceAgent proto
    agent_category: Optional[str] = None
    system_message: Optional[str] = None
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    subscriptions: Optional[str] = None
    tone: Optional[str] = None
    files: Optional[List[FileEntry]] = Field(default_factory=list)
    urls: Optional[List[UrlEntry]] = Field(default_factory=list)
    status: Optional[str] = None
    capabilities_id: Optional[str] = None
    source_workflow_id: Optional[str] = None
    source_agent_id: Optional[str] = None
    version: Optional[str] = None

    class Config:
        from_attributes = True


class AgentWithMCPsInDB(MarketplaceAgentResponse):
    mcps: Optional[List[MCPInDB]] = Field(default_factory=list)
    workflows: Optional[List[WorkflowInDB]] = Field(default_factory=list)


class MarketplaceWorkflowResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    workflow_url: Optional[str] = None  # Stores the workflow schema
    builder_url: Optional[str] = None  # Stores the builder schema
    start_nodes: Optional[List[Dict[str, Any]]] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    use_count: Optional[int] = 0
    execution_count: Optional[int] = 0
    average_rating: Optional[float] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    version: Optional[str] = "1.0.0"
    status: Optional[str] = "ACTIVE"
    visibility: str = "PUBLIC"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    workflow_definition: Optional[Dict[str, Any]] = None
    workflow_steps: Optional[List[Dict[str, Any]]] = None
    is_added: Optional[bool] = False
    available_nodes: Optional[List[Dict[str, Any]]] = None
    source_workflow_id: Optional[str] = None  # ID of the original workflow this was created from
    source_version_id: Optional[str] = None  # ID of the specific version this was cloned from

    class Config:
        from_attributes = True


class MarketplaceMCPResponse(BaseModel):
    id: str
    name: str
    logo: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    visibility: str = "PUBLIC"
    hosted_url: Optional[str] = None
    mcp_type: Optional[str] = None
    git_url: Optional[str] = None
    mcp_tools_config: Optional[dict] = None
    category: Optional[str] = None
    component_category: Optional[str] = None

    @validator("mcp_tools_config", pre=True)
    def validate_mcp_tools_config(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v

    class Config:
        from_attributes = True


class PaginatedMarketplaceAgentResponse(BaseModel):
    data: List[MarketplaceAgentResponse]
    metadata: PaginationMetadata


class PaginatedMarketplaceWorkflowResponse(BaseModel):
    data: List[MarketplaceWorkflowResponse]
    metadata: PaginationMetadata


class PaginatedMarketplaceMCPResponse(BaseModel):
    data: List[MarketplaceMCPResponse]
    metadata: PaginationMetadata


class MarketplaceItemTypeEnum(str, Enum):
    AGENT = "AGENT"
    WORKFLOW = "WORKFLOW"
    MCP = "MCP"


class MarketplaceItemResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    item_type: MarketplaceItemTypeEnum
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    visibility: str = "PUBLIC"
    # Agent-specific fields
    avatar: Optional[str] = None
    department: Optional[str] = None
    # Workflow-specific fields
    version: Optional[str] = None
    # MCP-specific fields
    sse_url: Optional[str] = None

    class Config:
        from_attributes = True


class CombinedMarketplaceResponse(BaseModel):
    data: List[MarketplaceItemResponse]
    metadata: PaginationMetadata


# Detailed Agent Response
class MarketplaceAgentDetail(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    avatar: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    visibility: str = "PUBLIC"
    system_message: Optional[str] = None
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    knowledge_base_ids: Optional[List[str]] = None
    workflow_ids: Optional[List[str]] = None
    mcp_server_ids: Optional[List[str]] = None
    category: Optional[CategoryEnum] = None
    # Additional fields from MarketplaceAgent proto
    agent_category: Optional[str] = None
    model_api_key: Optional[str] = None
    agent_topic_type: Optional[str] = None
    subscriptions: Optional[str] = None
    tone: Optional[str] = None
    files: Optional[List[str]] = None
    urls: Optional[List[str]] = None
    use_count: Optional[int] = None
    status: Optional[str] = None
    is_a2a: Optional[bool] = None
    is_customizable: Optional[bool] = None
    capabilities_id: Optional[str] = None
    example_prompts: Optional[List[str]] = None
    capabilities: Optional[AgentCapabilitiesInDB] = Field(default=None, alias="agent_capabilities")
    is_added: Optional[bool] = False
    source_workflow_id: Optional[str] = None
    source_agent_id: Optional[str] = None
    version: Optional[str] = None

    class Config:
        from_attributes = True


class MarketplaceAgentDetailResponse(BaseModel):
    success: bool
    message: str
    agent: AgentWithMCPsInDB


# Detailed Workflow Response
class MarketplaceWorkflowDetail(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    workflow_url: Optional[str] = None  # Stores the workflow schema
    builder_url: Optional[str] = None  # Stores the builder schema
    start_nodes: Optional[List[Dict[str, Any]]] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    use_count: Optional[int] = 0
    execution_count: Optional[int] = 0
    average_rating: Optional[float] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    version: Optional[str] = "1.0.0"
    status: Optional[str] = "ACTIVE"
    visibility: str = "PUBLIC"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    workflow_definition: Optional[Dict[str, Any]] = None
    workflow_steps: Optional[List[Dict[str, Any]]] = None
    is_added: Optional[bool] = False
    available_nodes: Optional[List[Dict[str, Any]]] = None
    source_workflow_id: Optional[str] = None  # ID of the original workflow this was created from
    source_version_id: Optional[str] = None  # ID of the specific version this was cloned from
    has_updates: Optional[bool] = False
    current_version_id: Optional[str] = None

    class Config:
        from_attributes = True


class MarketplaceWorkflowDetailResponse(BaseModel):
    success: bool
    message: str
    workflow: MarketplaceWorkflowDetail


# Detailed MCP Response
class MarketplaceMCPDetail(BaseModel):
    id: str
    name: str
    logo: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    hosted_url: Optional[str] = None
    mcp_type: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    visibility: str = "PUBLIC"
    git_url: Optional[str] = None
    api_documentation: Optional[str] = None
    capabilities: Optional[List[str]] = None
    mcp_tools_config: Optional[dict] = None
    department: Optional[str] = None
    is_added: Optional[bool] = False
    component_category: Optional[McpComponentCategory] = None
    repo_name: Optional[str] = None
    git_user_name: Optional[str] = None
    integrations: Optional[List[str]] = None
    url: Optional[str] = None

    @validator("mcp_tools_config", pre=True)
    def validate_mcp_tools_config(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v

    class Config:
        from_attributes = True


class MarketplaceMCPDetailResponse(BaseModel):
    success: bool
    message: str
    mcp: MarketplaceMCPDetail


# Rate Marketplace Item Request/Response
class RateMarketplaceItemRequest(BaseModel):
    item_id: str
    rating: float = Field(..., ge=1.0, le=5.0, description="Rating value between 1.0 and 5.0")
    item_type: MarketplaceItemTypeEnum


class RateMarketplaceItemResponse(BaseModel):
    success: bool
    message: str
    item_id: str
    item_type: MarketplaceItemTypeEnum
    rating: float
    average_rating: Optional[float] = None


# Use Marketplace Item Request/Response
class UseMarketplaceItemRequest(BaseModel):
    item_id: str
    item_type: MarketplaceItemTypeEnum


class UseMarketplaceItemResponse(BaseModel):
    success: bool
    message: str
    item_id: str
    item_type: MarketplaceItemTypeEnum
    use_count: int
    # Optional fields for returning created AI IDs
    agent_id: Optional[str] = None  # For agent items - ID of the created/used agent
    workflow_id: Optional[str] = None  # For workflow items - ID of the created/used workflow


class UseMarketplaceAgentRequest(BaseModel):
    agent_id: str
    mcp_ids: List[str]
    workflow_ids: List[str]


class WorkflowStats(BaseModel):
    total_count_to_add: int = Field(
        ..., description="Total number of workflows intended to be added"
    )
    successfully_added: int = Field(..., description="Number of workflows successfully added")
    failed_to_add: int = Field(..., description="Number of workflows that failed to add")


class MCPStats(BaseModel):
    total_count_to_add: int = Field(..., description="Total number of MCPs intended to be added")
    successfully_added: int = Field(..., description="Number of MCPs successfully assigned")
    failed_to_add: int = Field(..., description="Number of MCPs that failed to assign")


class AgentAssignmentStats(BaseModel):
    workflows: WorkflowStats
    mcps: MCPStats


class UseMarketplaceAgentResponse(BaseModel):
    success: bool = Field(
        ..., description="Indicates if the agent was successfully added to the workspace"
    )
    message: str = Field(..., description="Details about the result of the operation")
    agent_id: Optional[str] = Field(None, description="ID of the created or assigned agent")
    agent: Optional[AgentAssignmentStats] = Field(
        None, description="Assignment stats for workflows and MCPs"
    )
    use_count: Optional[int] = Field(0, description="How many times this agent has been used")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Agent successfully added to workspace.",
                "agent_id": "71d5e69d-809b-4829-ac32-c37b7c132248",
                "agent": {
                    "workflows": {
                        "total_count_to_add": 2,
                        "successfully_added": 2,
                        "failed_to_add": 0,
                    },
                    "mcps": {"total_count_to_add": 1, "successfully_added": 1, "failed_to_add": 0},
                },
                "use_count": 3,
            }
        }
