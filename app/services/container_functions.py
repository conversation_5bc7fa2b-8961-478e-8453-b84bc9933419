# app/services/mcp_config_service.py
import asyncio
from datetime import datetime, timezone
import shlex
import uuid
import grpc

import structlog
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, or_
from app.db.session import SessionLocal
from app.models.mcp_schema import McpConfig, McpDeployment, UserMcpAssignment
from app.models.mcp_rating import McpRating
from app.grpc import mcp_pb2, mcp_pb2_grpc
from app.utils.constants.constants import (
    DeploymentStatus,
    ContainerStatus,
)
from app.utils.kafka.kafka_service import KafkaProducer

from app.core.config import settings

from app.utils.secret_manager.secret_manager import EncryptionManager
from app.utils.stdio_deployment import SSHDockerService

secret_manager = EncryptionManager()
logger = structlog.get_logger(__name__)


class ContainerFunctionService(mcp_pb2_grpc.MCPServiceServicer):
    def __init__(self):
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def CreateContainer(
        self, request: mcp_pb2.CreateContainerRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.CreateContainerResponse:
        db = self.get_db()
        logger.info(
            f"gRPC CreateContainer request for MCP ID: {request.mcp_id}, User ID: {request.user_id}, Type: {request.type}"
        )

        if request.type.lower() != "stdio":
            message = f"Unsupported deployment type: {request.type}. Only 'stdio' is supported by this endpoint."
            logger.warning(message)
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(message)
            return mcp_pb2.CreateContainerResponse(success=False, message=message)

        db_deployment = None

        try:
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp_config:
                message = f"MCP with ID {request.mcp_id} not found."
                logger.warning(message)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return mcp_pb2.CreateContainerResponse(success=False, message=message)

            # --- CHECK FOR EXISTING RUNNING CONTAINER ---
            existing_running_deployment = (
                db.query(McpDeployment)
                .filter_by(
                    mcp_id=request.mcp_id,
                    user_id=request.user_id,
                    status=ContainerStatus.RUNNING.value,
                )
                .first()
            )

            if existing_running_deployment:
                message = (
                    f"A container is already running for MCP ID '{request.mcp_id}' "
                    f"with container name '{existing_running_deployment.container_name}' "
                )
                logger.info(message)
                return mcp_pb2.CreateContainerResponse(
                    success=True,
                    message=message,
                    container_id=existing_running_deployment.container_name,
                )

            if not mcp_config.image_name:
                message = f"MCP with ID {request.mcp_id} does not have an image_name configured."
                logger.warning(message)
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(message)
                return mcp_pb2.CreateContainerResponse(success=False, message=message)

            image_name_to_use = mcp_config.image_name
            deployment_id = str(uuid.uuid4())
            docker_container_name = f"{request.mcp_id}_{request.user_id}"

            # Create or get user assignment for tracking
            assignment = (
                db.query(UserMcpAssignment)
                .filter_by(user_id=request.user_id, mcp_id=request.mcp_id)
                .first()
            )

            if not assignment:
                assignment = UserMcpAssignment(
                    user_id=request.user_id,
                    mcp_id=request.mcp_id,
                )
                db.add(assignment)
                db.commit()

            # Handle environment variables from request
            env_vars_for_docker_list = []
            if request.env_vars:
                env_vars_for_docker_list = [f"{kv.key}={kv.value}" for kv in request.env_vars]
                logger.info(f"Using {len(request.env_vars)} environment variables from request")

            db_deployment = McpDeployment(
                id=deployment_id,
                mcp_id=request.mcp_id,
                user_id=request.user_id,
                container_name=docker_container_name,
                image_name=image_name_to_use,
                status=ContainerStatus.RUNNING.value, # Assuming STARTING is not a required persistent state
            )
            db.add(db_deployment)
            db.commit()
            db.refresh(db_deployment)
            logger.info(f"McpDeployment record {deployment_id} created with status 'STARTING'.")

            actual_docker_container_id = None
            try:
                ssh_config_dict = {
                    "ssh_host": settings.DEFAULT_SSH_HOST,
                    "ssh_user": settings.DEFAULT_SSH_USER,
                    "ssh_key_content": settings.DEFAULT_SSH_KEY_CONTENT,
                    "container_name": docker_container_name,
                }

                async def _perform_create():
                    async with SSHDockerService(ssh_config_dict) as ssh_service:
                        logger.info(
                            f"Attempting to create Docker container '{ssh_service.container_name}' "
                            f"with image '{image_name_to_use}'."
                        )

                        container_options_str = None
                        if env_vars_for_docker_list:
                            env_option_parts = [
                                f"-e {shlex.quote(env_str)}" for env_str in env_vars_for_docker_list
                            ]
                            container_options_str = " ".join(env_option_parts)
                            logger.debug(f"Formatted container options: {container_options_str}")

                        return await ssh_service.create_container(
                            image_tag=image_name_to_use, container_options=container_options_str
                        )

                actual_docker_container_id = asyncio.run(_perform_create())

                logger.info(
                    f"Docker container '{docker_container_name}' created successfully "
                    f"(Docker ID: {actual_docker_container_id})."
                )

                db_deployment.status = ContainerStatus.RUNNING.value
                db_deployment.updated_at = datetime.now(timezone.utc)
                db.commit()
                db.refresh(db_deployment)

                print(f"[CONATINER_NAME] {db_deployment.container_name}")

                return mcp_pb2.CreateContainerResponse(
                    success=True,
                    message=f"Container '{docker_container_name}' created successfully.",
                    container_id=docker_container_name,
                )

            except Exception as launch_exc:
                error_message = (
                    f"Failed to launch container '{docker_container_name}': {str(launch_exc)}"
                )
                logger.error(error_message, exc_info=True)
                if db_deployment:
                    # Assuming "ERROR" maps to a stopped or failed state in the enum
                    # If a distinct ERROR state is needed, it should be added to the enum
                    db_deployment.status = ContainerStatus.STOPPED.value
                    db_deployment.updated_at = datetime.now(timezone.utc)
                    db.commit()
                    db.refresh(db_deployment)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(error_message)
                return mcp_pb2.CreateContainerResponse(
                    success=False,
                    message=error_message,
                    container_id=docker_container_name,
                )

        except Exception as e:
            if "db" in locals() and db.is_active:
                db.rollback()
            error_message = f"Internal server error during container creation: {str(e)}"
            logger.error(error_message, exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal server error occurred.")
            return mcp_pb2.CreateContainerResponse(
                success=False, message="An internal server error occurred."
            )

        finally:
            if "db" in locals() and db:
                db.close()

    def StopContainer(
        self, request: mcp_pb2.StopContainerRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.StopContainerResponse:
        db = self.get_db()
        docker_container_name_from_request = request.container_id
        user_id_from_request = request.user_id  # You might not use these if not filtering DB query
        mcp_id_from_request = request.mcp_id  # You might not use these

        logger.info(
            f"gRPC StopContainer request for Docker Container Name: {docker_container_name_from_request}, "
            f"User ID: {user_id_from_request}, MCP ID: {mcp_id_from_request}"
        )

        try:
            # Fetch McpDeployment record... (your existing logic)
            db_deployment = (
                db.query(McpDeployment)
                .filter(McpDeployment.container_name == docker_container_name_from_request)
                .first()
            )

            if not db_deployment:
                # ... (handle not found)
                message = f"No McpDeployment record found for Docker container '{docker_container_name_from_request}'"
                logger.warning(message)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return mcp_pb2.StopContainerResponse(success=False, message=message)

            # ... (check if already stopped, your existing logic) ...
            if db_deployment.status == ContainerStatus.STOPPED.value:
                message = f"Container '{docker_container_name_from_request}' (Deployment DB ID: {db_deployment.id}) is already in DB status '{db_deployment.status}'."
                logger.info(message)
                return mcp_pb2.StopContainerResponse(success=True, message=message)

            force_stop = False  # Default
            stop_timeout = 30  # Default

            ssh_config_dict = {
                "ssh_host": settings.DEFAULT_SSH_HOST,
                "ssh_user": settings.DEFAULT_SSH_USER,
                "ssh_key_content": settings.DEFAULT_SSH_KEY_CONTENT,
                "container_name": docker_container_name_from_request,
            }

            try:
                # Use an inner async function to manage the context
                async def _perform_stop():
                    async with SSHDockerService(ssh_config_dict) as ssh_service:
                        logger.info(
                            f"Attempting to stop Docker container '{ssh_service.container_name}' "
                            f"(Deployment DB ID: {db_deployment.id}). Force: {force_stop}, Timeout: {stop_timeout}s"
                        )
                        return await ssh_service.stop_container(
                            force=force_stop, timeout=stop_timeout
                        )

                stopped_successfully = asyncio.run(_perform_stop())

                current_time = datetime.now(timezone.utc)
                if stopped_successfully:
                    db_deployment.status = ContainerStatus.STOPPED.value
                    # ... (update db, commit, refresh, success response) ...
                    message = (
                        f"Container '{docker_container_name_from_request}' (Deployment DB ID: {db_deployment.id}) "
                        f"stopped successfully. DB status updated to STOPPED."
                    )
                else:
                    # Container was not running on host
                    db_deployment.status = ContainerStatus.STOPPED.value
                    # ... (update db, commit, refresh, success response) ...
                    message = (
                        f"Container '{docker_container_name_from_request}' (Deployment DB ID: {db_deployment.id}) "
                        f"was not running on the host. DB status has been updated to STOPPED."
                    )

                db_deployment.updated_at = current_time
                db.commit()
                db.refresh(db_deployment)
                logger.info(message)
                return mcp_pb2.StopContainerResponse(success=True, message=message)

            except RuntimeError as runtime_exc:
                # ... (handle runtime_exc, update DB to ERROR, response) ...
                error_message = (
                    f"Failed to stop container '{docker_container_name_from_request}' "
                    f"(Deployment DB ID: {db_deployment.id}): {str(runtime_exc)}"
                )
                logger.error(error_message, exc_info=True)
                db.commit()
                db.refresh(db_deployment)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(error_message)
                return mcp_pb2.StopContainerResponse(success=False, message=error_message)

        except Exception as e:
            # ... (general exception handling, db.rollback, response) ...
            if "db" in locals() and db.is_active:
                db.rollback()
            error_message = f"Internal server error during StopContainer for Docker container '{request.container_id if request else 'unknown'}': {str(e)}"
            logger.error(error_message, exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal server error occurred.")
            return mcp_pb2.StopContainerResponse(
                success=False, message="An internal server error occurred."
            )
        finally:
            if "db" in locals() and db:
                db.close()

    def DeleteContainer(
        self, request: mcp_pb2.DeleteContainerRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.DeleteContainerResponse:
        db = self.get_db()
        docker_container_name_from_request = request.container_id

        logger.info(
            f"gRPC DeleteContainer request for Docker Container Name: {docker_container_name_from_request}, "
        )

        try:
            # 
            db_deployment = (
            db.query(McpDeployment)
            .filter(
                McpDeployment.container_name == docker_container_name_from_request,
                McpDeployment.status == ContainerStatus.RUNNING.value,
            )
            .order_by(McpDeployment.created_at.desc())  # If multiple still exist
            .first()
        )
            deployment_id_for_logging = db_deployment.id if db_deployment else "N/A"

            if not db_deployment:
                message = f"No McpDeployment record found for Docker container '{docker_container_name_from_request}'"
                logger.warning(message)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return mcp_pb2.DeleteContainerResponse(success=False, message=message)

            # ssh_config_dict = {
            #     "ssh_host": settings.DEFAULT_SSH_HOST,
            #     "ssh_user": settings.DEFAULT_SSH_USER,
            #     "ssh_key_content": settings.DEFAULT_SSH_KEY_CONTENT,
            #     "container_name": docker_container_name_from_request,
            # }

            # stop_message = ""
            try:
                final_message = f"Container {docker_container_name_from_request} still running "
            #     # Inner async function for stopping
            #     async def _perform_stop():
            #         async with SSHDockerService(ssh_config_dict) as ssh_service:
            #             logger.info(
            #                 f"Attempting to stop Docker container '{ssh_service.container_name}' "
            #                 f"(Deployment DB ID: {deployment_id_for_logging})"
            #             )
            #             return await ssh_service.stop_container(force=False, timeout=30)

            #     stopped_successfully = asyncio.run(_perform_stop())

            #     current_time = datetime.now(timezone.utc)
            #     db_deployment.status = ContainerStatus.STOPPED.value
            #     db_deployment.updated_at = current_time
            #     db.commit()
            #     db.refresh(db_deployment)

            #     stop_message = (
            #         f"Container '{docker_container_name_from_request}' stopped successfully. "
            #         f"Deployment DB status updated to STOPPED."
            #         if stopped_successfully
            #         else f"Container '{docker_container_name_from_request}' was already stopped or not running. "
            #         f"Deployment DB status still updated to STOPPED."
            #     )
            #     logger.info(stop_message)

            # except RuntimeError as stop_error:
            #     stop_error_msg = (
            #         f"Failed to stop container '{docker_container_name_from_request}' before deletion "
            #         f"(Deployment DB ID: {deployment_id_for_logging}): {str(stop_error)}"
            #     )
            #     logger.error(stop_error_msg, exc_info=True)
            #     context.set_code(grpc.StatusCode.INTERNAL)
            #     context.set_details(stop_error_msg)
            #     return mcp_pb2.DeleteContainerResponse(success=False, message=stop_error_msg)

            # # Proceed with deletion
            # try:

            #     async def _perform_delete():
            #         async with SSHDockerService(ssh_config_dict) as ssh_service:
            #             logger.info(
            #                 f"Attempting to delete Docker container '{ssh_service.container_name}' "
            #                 f"(Deployment DB ID: {deployment_id_for_logging})"
            #             )
            #             return await ssh_service.delete_container(force=False)

            #     deleted_successfully = asyncio.run(_perform_delete())

            #     current_time = datetime.now(timezone.utc)
            #     db_deployment.status = ContainerStatus.DELETED.value
            #     db_deployment.updated_at = current_time
            #     db.commit()
            #     db.refresh(db_deployment)

            #     delete_msg = (
            #         f"Container '{docker_container_name_from_request}' deleted from host. "
            #         f"Deployment DB status updated to DELETED."
            #         if deleted_successfully
            #         else f"Container '{docker_container_name_from_request}' not found on host. "
            #         f"Deployment DB status still updated to DELETED."
            #     )
            #     logger.info(delete_msg)
            #     final_message = f"{stop_message} {delete_msg}"
                # return mcp_pb2.DeleteContainerResponse(success=True, message=final_message)

                return mcp_pb2.DeleteContainerResponse(success=True, message=final_message)
            except RuntimeError as delete_error:
                delete_error_msg = (
                    f"Failed to delete container '{docker_container_name_from_request}' "
                    f"(Deployment DB ID: {deployment_id_for_logging}): {str(delete_error)}"
                )
                logger.error(delete_error_msg, exc_info=True)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(delete_error_msg)
                return mcp_pb2.DeleteContainerResponse(success=False, message=delete_error_msg)

        except Exception as e:
            if "db" in locals() and db.is_active:
                db.rollback()
            error_message = f"Internal server error during DeleteContainer for Docker container '{request.container_id if request else 'unknown'}': {str(e)}"
            logger.error(error_message, exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal server error occurred.")
            return mcp_pb2.DeleteContainerResponse(
                success=False, message="An internal server error occurred."
            )
        finally:
            if "db" in locals() and db:
                db.close()
