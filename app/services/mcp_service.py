# app/services/mcp_config_service.py
import asyncio
from datetime import datetime, timezone
import shlex
import uuid
import grpc
import json
import structlog
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, or_
from app.db.session import SessionLocal
from app.models.mcp_schema import McpConfig, McpDeployment
from app.models.mcp_rating import McpRating
from app.grpc import mcp_pb2, mcp_pb2_grpc
from app.utils.constants.constants import (
    DeploymentStatus,
    McpStatus,
    McpVisibility,
    McpCategory,
    UrlType,
)
from app.utils.kafka.kafka_service import KafkaProducer

from app.utils.MCP.fetch_tools import get_mcp_tools, tools_to_json_response

from app.utils.google_pubsub import publish_deployment_message

from app.core.config import settings


from app.utils.stdio_deployment import SSHDockerService

from app.utils.MCP.fetch_tools_stdio import SSHMCPClient

from app.services.container_functions import ContainerFunctionService
from app.services.marketplace_functions import MarketplaceFunctionService
from app.services.mcp_functions import MCPFunctionService

logger = structlog.get_logger(__name__)


class MCPConfigService(mcp_pb2_grpc.MCPServiceServicer):
    def __init__(self):
        self.kafka_producer = KafkaProducer()
        self.mcp_functions = MCPFunctionService()
        self.container_functions = ContainerFunctionService()
        self.marketplace_functions = MarketplaceFunctionService()

    def createMCP(
        self, request: mcp_pb2.CreateMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        try:
            logger.info(f"gRPC: create_mcp_request received for name: {request.name}")
            response = self.mcp_functions.createMCP(request, context)
            return response
        except Exception as e:
            logger.error(f"Error creating MCP: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error creating MCP: {str(e)}")
            return mcp_pb2.MCPResponse(success=False, message=str(e))

    def getMCP(
        self, request: mcp_pb2.GetMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        try:
            logger.info(f"gRPC: get_mcp_request received for id: {request.id}")
            response = self.mcp_functions.getMCP(request, context)
            return response
        except Exception as e:
            logger.error(f"Error getting MCP: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting MCP: {str(e)}")
            return mcp_pb2.MCPResponse(success=False, message=str(e))

    def updateMCP(
        self, request: mcp_pb2.UpdateMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        try:
            logger.info(f"gRPC: update_mcp_request received for id: {request.id}")
            response = self.mcp_functions.updateMCP(request, context)
            return response
        except Exception as e:
            logger.error(f"Error updating MCP: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating MCP: {str(e)}")
            return mcp_pb2.MCPResponse(success=False, message=str(e))

    def deleteMCP(
        self, request: mcp_pb2.DeleteMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.DeleteMCPResponse:
        try:
            logger.info(f"gRPC: delete_mcp_request received for id: {request.id}")
            response = self.mcp_functions.deleteMCP(request, context)
            return response
        except Exception as e:
            logger.error(f"Error deleting MCP: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error deleting MCP: {str(e)}")
            return mcp_pb2.DeleteMCPResponse(success=False, message=str(e))

    def listMCPs(
        self, request: mcp_pb2.ListMCPsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ListMCPsResponse:
        try:
            logger.info(f"gRPC: list_mcps_request received")
            response = self.mcp_functions.listMCPs(request, context)
            return response
        except Exception as e:
            logger.error(f"Error listing MCPs: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing MCPs: {str(e)}")
            return mcp_pb2.ListMCPsResponse()

    def getMCPsByIds(
        self, request: mcp_pb2.GetMCPsByIdsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ListMCPsResponse:
        try:
            logger.info(f"gRPC: get_mcps_by_ids_request received for ids: {request.ids}")
            response = self.mcp_functions.getMCPsByIds(request, context)
            return response
        except Exception as e:
            logger.error(f"Error getting MCPs by ids: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting MCPs by ids: {str(e)}")
            return mcp_pb2.ListMCPsResponse()

    def listMCPsMarketplace(
        self, request: mcp_pb2.GetMarketplaceMCPsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetMarketplaceMCPsResponse:
        try:
            logger.info(f"gRPC: list_mcps_marketplace_request received")
            response = self.marketplace_functions.getMarketplaceMCPs(request, context)
            return response
        except Exception as e:
            logger.error(f"Error listing MCPs for marketplace: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)

    def getMarketplaceMCPs(
        self, request: mcp_pb2.GetMarketplaceMCPsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetMarketplaceMCPsResponse:
        try:
            logger.info(f"gRPC: get_marketplace_mcps_request received")
            response = self.marketplace_functions.getMarketplaceMCPs(request, context)
            return response
        except Exception as e:
            logger.error(f"Error getting marketplace MCPs: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting marketplace MCPs: {str(e)}")
            return mcp_pb2.GetMarketplaceMCPsResponse(success=False, message=str(e))

    def getMarketplaceMCPDetail(
        self, request: mcp_pb2.GetMarketplaceMCPDetailRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetMarketplaceMCPDetailResponse:
        try:
            logger.info(f"gRPC: get_marketplace_mcp_detail_request received for id: {request.id}")
            response = self.marketplace_functions.getMarketplaceMCPDetail(request, context)
            return response
        except Exception as e:
            logger.error(f"Error getting marketplace MCP detail: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting marketplace MCP detail: {str(e)}")
            return mcp_pb2.GetMarketplaceMCPDetailResponse(success=False, message=str(e))

    def rateMCP(
        self, request: mcp_pb2.RateMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.RateMCPResponse:
        try:
            logger.info(f"gRPC: rate_mcp_request received for id: {request.mcp_id}")
            response = self.marketplace_functions.rateMCP(request, context)
            return response
        except Exception as e:
            logger.error(f"Error rating MCP: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error rating MCP: {str(e)}")
            return mcp_pb2.RateMCPResponse(success=False, message=str(e))

    def useMCP(
        self, request: mcp_pb2.UseMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.UseMCPResponse:
        try:
            logger.info(f"gRPC: use_mcp_request received for id: {request.mcp_id}")
            response = self.marketplace_functions.useMCP(request, context)
            return response
        except Exception as e:
            logger.error(f"Error using MCP: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error using MCP: {str(e)}")
            return mcp_pb2.UseMCPResponse(success=False, message=str(e))

    def UpdateDeploymentStatus(
        self, request: mcp_pb2.UpdateDeploymentStatusRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        try:
            logger.info(f"gRPC: update_deployment_status_request received for id: {request.id}")
            response = self.mcp_functions.UpdateDeploymentStatus(request, context)
            return response
        except Exception as e:
            logger.error(f"Error updating deployment status: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating deployment status: {str(e)}")
            return mcp_pb2.MCPResponse(success=False, message=str(e))

    def CreateContainer(
        self, request: mcp_pb2.CreateContainerRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.CreateContainerResponse:
        try:
            logger.info(f"gRPC: create_container_request received for id: {request.mcp_id}")
            response = self.container_functions.CreateContainer(request, context)
            return response
        except Exception as e:
            logger.error(f"Error creating container: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error creating container: {str(e)}")
            return mcp_pb2.CreateContainerResponse(success=False, message=str(e))

    def StopContainer(
        self, request: mcp_pb2.StopContainerRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.StopContainerResponse:
        try:
            logger.info(f"gRPC: stop_container_request received for id: {request.container_id}")
            response = self.container_functions.StopContainer(request, context)
            return response
        except Exception as e:
            logger.error(f"Error stopping container: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error stopping container: {str(e)}")
            return mcp_pb2.StopContainerResponse(success=False, message=str(e))

    def GetContainerStatus(
        self, request: mcp_pb2.GetContainerStatusRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetContainerStatusResponse:
        try:
            logger.info(
                f"gRPC: get_container_status_request received for id: {request.container_id}"
            )
            response = self.container_functions.GetContainerStatus(request, context)
            return response
        except Exception as e:
            logger.error(f"Error getting container status: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting container status: {str(e)}")
            return mcp_pb2.GetContainerStatusResponse(success=False, message=str(e))

    def DeleteContainer(
        self, request: mcp_pb2.DeleteContainerRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.DeleteContainerResponse:
        try:
            logger.info(f"gRPC: delete_container_request received for id: {request.container_id}")
            response = self.container_functions.DeleteContainer(request, context)
            return response
        except Exception as e:
            logger.error(f"Error deleting container: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error deleting container: {str(e)}")
            return mcp_pb2.DeleteContainerResponse(success=False, message=str(e))

    def UpdateToolOutputSchema(
        self, request: mcp_pb2.UpdateToolOutputSchemaRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        try:
            logger.info(f"gRPC: update_tool_output_schema_request received for mcp_id: {request.mcp_id}, tool_name: {request.tool_name}")
            response = self.mcp_functions.UpdateToolOutputSchema(request, context)
            return response
        except Exception as e:
            logger.error(f"Error updating tool output schema: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating tool output schema: {str(e)}")
            return mcp_pb2.MCPResponse(success=False, message=str(e))

    def ToggleMcpVisibility(
        self, request: mcp_pb2.ToggleMcpVisibilityRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ToggleMcpVisibilityResponse:
        try:
            logger.info(f"gRPC: toggle_mcp_visibility_request received for mcp_id: {request.mcp_id}")
            response = self.mcp_functions.ToggleMcpVisibility(request, context)
            print(f"[GRPC_SERVER] gRPC response: {response}")
            return response
        except Exception as e:
            logger.error(f"Error toggling MCP visibility: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error toggling MCP visibility: {str(e)}")
            return mcp_pb2.ToggleMcpVisibilityResponse(success=False, message=str(e))

    def UpdateMcpEnvVars(
        self, request: mcp_pb2.UpdateMcpEnvVarsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.UpdateMcpEnvVarsResponse:
        try:
            logger.info(f"gRPC: update mcp env variables request received for mcp_id: {request.mcp_id}")
            response = self.mcp_functions.UpdateMcpEnvVars(request, context)
            print(f"[GRPC_SERVER] gRPC response: {response}")
            return response
        except Exception as e:
            logger.error(f"Error toggling MCP visibility: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error toggling MCP visibility: {str(e)}")
            return mcp_pb2.UpdateMcpEnvVarsResponse(success=False, message=str(e))
    
    
    def GetMcpEnvVars(
        self, request: mcp_pb2.GetMcpEnvVarsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetMcpEnvVarsResponse:
        try:
            logger.info(f"gRPC: update mcp env variables request received for mcp_id: {request.mcp_id}")
            response = self.mcp_functions.GetMcpEnvVars(request, context)
            print(f"[GRPC_SERVER] gRPC response: {response}")
            return response
        except Exception as e:
            logger.error(f"Error toggling MCP visibility: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error toggling MCP visibility: {str(e)}")
            return mcp_pb2.GetMcpEnvVarsResponse(success=False, message=str(e))

    def RefreshMCP(self, request: mcp_pb2.RefreshMCPRequest, context: grpc.ServicerContext) -> mcp_pb2.MCPResponse:
        try:
            logger.info(f"gRPC: refresh_mcp request received for mcp_id: {request.mcp_id}")
            response = self.mcp_functions.refresh_mcp(request, context)
            print(f"[GRPC_SERVER] gRPC response: {response}")
            return response
        except Exception as e:
            logger.error(f"Error refreshing MCP: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error refreshing MCP: {str(e)}")
            return mcp_pb2.MCPResponse(success=False, message=str(e))
    
    def makeQuickTool(self, request: mcp_pb2.MakeQuickToolRequest, context: grpc.ServicerContext) -> mcp_pb2.QuickToolResponse:
        try:
            logger.info(f"gRPC: make_quick_tool request received for mcp_id: {request.mcp_id}")
            response = self.mcp_functions.makeQuickTool(request, context)
            print(f"[GRPC_SERVER] gRPC response: {response}")
            return response
        except Exception as e:
            logger.error(f"Error making MCP quick tool: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error making MCP quick tool: {str(e)}")
            return mcp_pb2.QuickToolResponse(success=False, message=str(e))
    
    def removeQuickTool(self, request: mcp_pb2.RemoveQuickToolRequest, context: grpc.ServicerContext) -> mcp_pb2.QuickToolResponse:
        try:
            logger.info(f"gRPC: remove_quick_tool request received for mcp_id: {request.mcp_id}")
            response = self.mcp_functions.removeQuickTool(request, context)
            print(f"[GRPC_SERVER] gRPC response: {response}")
            return response
        except Exception as e:
            logger.error(f"Error removing MCP quick tool: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error removing MCP quick tool: {str(e)}")
            return mcp_pb2.QuickToolResponse(success=False, message=str(e))