"""
Dummy data for MCP service.
This file contains dummy data for MCP configurations.
"""

import uuid
from datetime import datetime, timezone
from app.utils.constants.constants import (
    McpVisibility,
    McpStatus,
    McpOwnerType,
    McpCategory,
    UrlType,
)

# Generate some consistent UUIDs for reference
USER_ID_1 = "user-" + str(uuid.uuid4())
USER_ID_2 = "user-" + str(uuid.uuid4())
ENTERPRISE_ID_1 = "enterprise-" + str(uuid.uuid4())
PLATFORM_ID = "platform-" + str(uuid.uuid4())

# Sample MCP tools configuration
SAMPLE_MCP_TOOLS_CONFIG = {
    "tools": [
        {
            "name": "web_search",
            "description": "Search the web for information",
            "parameters": {
                "query": {
                    "type": "string",
                    "description": "The search query"
                },
                "num_results": {
                    "type": "integer",
                    "description": "Number of results to return",
                    "default": 5
                }
            }
        },
        {
            "name": "calculator",
            "description": "Perform mathematical calculations",
            "parameters": {
                "expression": {
                    "type": "string",
                    "description": "The mathematical expression to evaluate"
                }
            }
        }
    ]
}

# MCP Config Dummy Data
MCP_CONFIG_DUMMY_DATA = [
    {
        "id": "mcp-" + str(uuid.uuid4()),
        "name": "Web Search MCP",
        "description": "An MCP for performing web searches and retrieving information.",
        "logo": "https://example.com/logos/web_search.png",
        "mcp_tools_config": SAMPLE_MCP_TOOLS_CONFIG,
        "visibility": McpVisibility.PUBLIC,
        "owner_id": PLATFORM_ID,
        "owner_type": McpOwnerType.PLATFORM,
        "user_ids": None,
        "organization_user_ids": None,
        "use_count": 250,
        "average_rating": 4.7,
        "department": McpCategory.GENERAL,
        "tags": {
            "categories": ["search", "information retrieval"],
            "use_cases": ["research", "fact checking"],
            "complexity": "low"
        },
        "status": McpStatus.ACTIVE,
        "url": "https://api.example.com/mcp/web-search",
        "url_type": UrlType.HTTP,
        "git_url": "https://github.com/example/web-search-mcp",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "mcp-" + str(uuid.uuid4()),
        "name": "Data Analysis MCP",
        "description": "An MCP for analyzing and visualizing data.",
        "logo": "https://example.com/logos/data_analysis.png",
        "mcp_tools_config": {
            "tools": [
                {
                    "name": "analyze_data",
                    "description": "Analyze a dataset and provide insights",
                    "parameters": {
                        "data_url": {
                            "type": "string",
                            "description": "URL to the dataset"
                        },
                        "analysis_type": {
                            "type": "string",
                            "description": "Type of analysis to perform",
                            "enum": ["descriptive", "predictive", "prescriptive"]
                        }
                    }
                },
                {
                    "name": "visualize_data",
                    "description": "Create visualizations from data",
                    "parameters": {
                        "data_url": {
                            "type": "string",
                            "description": "URL to the dataset"
                        },
                        "chart_type": {
                            "type": "string",
                            "description": "Type of chart to create",
                            "enum": ["bar", "line", "scatter", "pie"]
                        }
                    }
                }
            ]
        },
        "visibility": McpVisibility.PUBLIC,
        "owner_id": PLATFORM_ID,
        "owner_type": McpOwnerType.PLATFORM,
        "user_ids": None,
        "organization_user_ids": None,
        "use_count": 180,
        "average_rating": 4.8,
        "department": McpCategory.ENGINEERING,
        "tags": {
            "categories": ["data analysis", "visualization"],
            "use_cases": ["business intelligence", "reporting"],
            "complexity": "medium"
        },
        "status": McpStatus.ACTIVE,
        "url": "https://api.example.com/mcp/data-analysis",
        "url_type": UrlType.HTTP,
        "git_url": "https://github.com/example/data-analysis-mcp",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "mcp-" + str(uuid.uuid4()),
        "name": "Marketing Analytics MCP",
        "description": "An MCP for analyzing marketing campaigns and performance.",
        "logo": "https://example.com/logos/marketing_analytics.png",
        "mcp_tools_config": {
            "tools": [
                {
                    "name": "campaign_analysis",
                    "description": "Analyze marketing campaign performance",
                    "parameters": {
                        "campaign_id": {
                            "type": "string",
                            "description": "ID of the marketing campaign"
                        },
                        "date_range": {
                            "type": "object",
                            "description": "Date range for analysis",
                            "properties": {
                                "start_date": {"type": "string", "format": "date"},
                                "end_date": {"type": "string", "format": "date"}
                            }
                        }
                    }
                },
                {
                    "name": "audience_segmentation",
                    "description": "Segment audience based on behavior",
                    "parameters": {
                        "data_source": {
                            "type": "string",
                            "description": "Source of audience data"
                        },
                        "segmentation_criteria": {
                            "type": "array",
                            "description": "Criteria for segmentation",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                }
            ]
        },
        "visibility": McpVisibility.PUBLIC,
        "owner_id": PLATFORM_ID,
        "owner_type": McpOwnerType.PLATFORM,
        "user_ids": None,
        "organization_user_ids": None,
        "use_count": 120,
        "average_rating": 4.6,
        "department": McpCategory.MARKETING,
        "tags": {
            "categories": ["marketing", "analytics"],
            "use_cases": ["campaign analysis", "audience segmentation"],
            "complexity": "medium"
        },
        "status": McpStatus.ACTIVE,
        "url": "https://api.example.com/mcp/marketing-analytics",
        "url_type": UrlType.HTTP,
        "git_url": "https://github.com/example/marketing-analytics-mcp",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    }
]

# MCP Rating Dummy Data
MCP_RATING_DUMMY_DATA = [
    {
        "id": "rating-" + str(uuid.uuid4()),
        "mcp_id": MCP_CONFIG_DUMMY_DATA[0]["id"],
        "user_id": USER_ID_1,
        "rating": 4.5,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "rating-" + str(uuid.uuid4()),
        "mcp_id": MCP_CONFIG_DUMMY_DATA[0]["id"],
        "user_id": USER_ID_2,
        "rating": 5.0,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "rating-" + str(uuid.uuid4()),
        "mcp_id": MCP_CONFIG_DUMMY_DATA[1]["id"],
        "user_id": USER_ID_1,
        "rating": 4.8,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    },
    {
        "id": "rating-" + str(uuid.uuid4()),
        "mcp_id": MCP_CONFIG_DUMMY_DATA[2]["id"],
        "user_id": USER_ID_2,
        "rating": 4.7,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    }
]
