import asyncio
import base64
import functools
import logging
import socket
import tempfile
from typing import Any, Callable, Dict, Optional, Type, Union
import structlog
from app.core.config import Settings

logger = structlog.get_logger(__name__)


class SSHDockerService:
    """SSH-based Docker service for building and deploying containers."""

    def __init__(self, ssh_config: Dict[str, str]):
        """
        Initialize SSH Docker service.

        Args:
            ssh_config: SSH configuration containing host, user, key_content, and container_name
        """
        self.ssh_host = ssh_config["ssh_host"]
        self.ssh_user = ssh_config["ssh_user"]

        # Handle encrypted SSH key content
        ssh_key_content_encrypted = ssh_config["ssh_key_content"]
        try:
            # Decrypt base64-encoded SSH key
            self.ssh_key_content = base64.b64decode(ssh_key_content_encrypted).decode("utf-8")
            logger.info("SSH key decrypted successfully")
        except Exception as e:
            # If decryption fails, assume it's already plain text
            self.ssh_key_content = ssh_key_content_encrypted
            logger.warning("SSH key decryption failed, using as plain text", error=str(e))

        self.container_name = ssh_config["container_name"]
        self.settings = Settings()
        self._ssh_key_file = None

    async def __aenter__(self):
        """Async context manager entry."""
        await self._setup_ssh_key()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._cleanup_ssh_key()

    async def _setup_ssh_key(self) -> None:
        """Setup SSH private key file."""
        try:
            # Create temporary file for SSH key
            with tempfile.NamedTemporaryFile(mode="w", suffix=".pem", delete=False) as temp_file:
                temp_file.write(self.ssh_key_content)
                self._ssh_key_file = temp_file.name

            # Set proper permissions for SSH key
            import os

            os.chmod(self._ssh_key_file, 0o600)

            logger.info("SSH key file created", key_file=self._ssh_key_file)

        except Exception as e:
            logger.error("Failed to setup SSH key", error=str(e))
            raise

    async def _cleanup_ssh_key(self) -> None:
        """Cleanup SSH private key file."""
        if self._ssh_key_file:
            try:
                import os

                os.unlink(self._ssh_key_file)
                logger.info("SSH key file cleaned up", key_file=self._ssh_key_file)
            except Exception as e:
                logger.warning("Failed to cleanup SSH key file", error=str(e))

    async def _run_ssh_command(self, command: str, timeout: float = 300.0) -> tuple[int, str, str]:
        """
        Run a command on the remote server via SSH.

        Args:
            command: Command to execute on remote server
            timeout: Command timeout in seconds

        Returns:
            Tuple of (return_code, stdout, stderr)
        """
        ssh_cmd = [
            "ssh",
            "-i",
            self._ssh_key_file,
            "-o",
            "StrictHostKeyChecking=no",
            "-o",
            "UserKnownHostsFile=/dev/null",
            "-o",
            "ConnectTimeout=30",
            f"{self.ssh_user}@{self.ssh_host}",
            command,
        ]

        logger.info("Executing SSH command", command=command)

        try:
            process = await asyncio.create_subprocess_exec(
                *ssh_cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)

            return process.returncode, stdout.decode(), stderr.decode()

        except asyncio.TimeoutError:
            logger.error(f"SSH command timed out after {timeout}s", command=command)
            try:
                process.terminate()
                await asyncio.wait_for(process.wait(), timeout=5.0)
            except:
                try:
                    process.kill()
                    await process.wait()
                except:
                    pass
            raise

    @staticmethod
    def is_network_error(exception: Exception) -> bool:
        """
        Check if an exception is likely a network connectivity issue.

        Args:
            exception: Exception to check

        Returns:
            True if the exception appears to be network-related
        """
        error_str = str(exception).lower()

        # DNS resolution errors
        dns_indicators = [
            "failed to resolve",
            "nodename nor servname provided",
            "name resolution error",
            "nameresolutionerror",
            "getaddrinfo failed",
            "temporary failure in name resolution",
        ]

        # Connection errors
        connection_indicators = [
            "connection error",
            "connectionerror",
            "max retries exceeded",
            "connection refused",
            "connection timeout",
            "network is unreachable",
            "no route to host",
        ]

        # SSL/TLS errors that might be network related
        ssl_indicators = [
            "ssl: certificate_verify_failed",
            "ssl handshake failed",
            "ssl connection error",
        ]

        all_indicators = dns_indicators + connection_indicators + ssl_indicators

        return any(indicator in error_str for indicator in all_indicators)

    async def check_network_connectivity(
        host: str = "*******", port: int = 53, timeout: float = 5.0
    ) -> bool:
        """
        Check basic network connectivity by attempting to connect to a reliable host.

        Args:
            host: Host to test connectivity to (default: Google DNS)
            port: Port to test (default: 53 for DNS)
            timeout: Connection timeout in seconds

        Returns:
            True if network connectivity is available
        """
        try:
            # Create a socket connection to test basic connectivity
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception:
            return False

    async def check_dns_resolution(hostname: str, timeout: float = 10.0) -> bool:
        """
        Check if DNS resolution is working for a specific hostname.

        Args:
            hostname: Hostname to resolve
            timeout: DNS resolution timeout in seconds

        Returns:
            True if DNS resolution succeeds
        """
        try:
            # Use asyncio's getaddrinfo for async DNS resolution
            loop = asyncio.get_event_loop()
            await asyncio.wait_for(loop.getaddrinfo(hostname, None), timeout=timeout)
            return True
        except (asyncio.TimeoutError, socket.gaierror, OSError):
            return False

    @staticmethod
    async def wait_for_network_recovery(
        max_wait_time: float = 60.0, check_interval: float = 5.0
    ) -> bool:
        """
        Wait for network connectivity to be restored.

        Args:
            max_wait_time: Maximum time to wait for recovery
            check_interval: How often to check connectivity

        Returns:
            True if network connectivity is restored within max_wait_time
        """
        start_time = asyncio.get_event_loop().time()

        while (asyncio.get_event_loop().time() - start_time) < max_wait_time:
            if await SSHDockerService.check_network_connectivity():
                # Also check Google Cloud DNS resolution
                if await SSHDockerService.check_dns_resolution("googleapis.com"):
                    logger.info("Network connectivity restored")
                    return True

            logger.warning(
                f"Network connectivity not available, retrying in {check_interval}s",
                extra={"elapsed_time": asyncio.get_event_loop().time() - start_time},
            )
            await asyncio.sleep(check_interval)

        logger.error(f"Network connectivity not restored within {max_wait_time}s")
        return False

    def retry_on_exception(
        max_attempts: int = 3,
        wait_min: float = 1.0,
        wait_max: float = 60.0,
        exception_types: Optional[Union[Type[Exception], tuple]] = None,
        before_sleep_log_level: int = logging.WARNING,
        network_aware: bool = True,
    ) -> Callable:
        """
        Decorator for retrying functions on specific exceptions with network-aware retry logic.

        Args:
            max_attempts: Maximum number of retry attempts
            wait_min: Minimum wait time between retries (seconds)
            wait_max: Maximum wait time between retries (seconds)
            exception_types: Exception types to retry on (default: Exception)
            before_sleep_log_level: Log level for retry messages
            network_aware: Enable network-aware retry logic for connectivity issues

        Returns:
            Decorated function with retry logic
        """
        if exception_types is None:
            exception_types = Exception

        def decorator(func: Callable) -> Callable:
            if asyncio.iscoroutinefunction(func):

                @functools.wraps(func)
                async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
                    last_exception = None

                    for attempt_num in range(max_attempts):
                        try:
                            return await func(*args, **kwargs)
                        except exception_types as e:
                            last_exception = e

                            if attempt_num == max_attempts - 1:
                                # Last attempt, re-raise the exception
                                raise

                            # Check if this is a network error and handle accordingly
                            if network_aware and SSHDockerService.is_network_error(e):
                                logger.warning(
                                    f"Network error detected on attempt {attempt_num + 1}/{max_attempts}: {e}",
                                    extra={
                                        "attempt": attempt_num + 1,
                                        "max_attempts": max_attempts,
                                    },
                                )

                                # For network errors, wait for connectivity to be restored
                                # Use longer wait times for network issues
                                network_wait_time = min(wait_max, wait_min * (2**attempt_num))

                                logger.info(
                                    f"Waiting for network recovery (up to {network_wait_time}s)"
                                )
                                network_recovered = (
                                    await SSHDockerService.wait_for_network_recovery(
                                        max_wait_time=network_wait_time, check_interval=2.0
                                    )
                                )

                                if not network_recovered:
                                    logger.warning(
                                        "Network connectivity not restored, proceeding with retry anyway"
                                    )

                                # Additional wait after network check
                                await asyncio.sleep(wait_min)
                            else:
                                # Standard exponential backoff for non-network errors
                                wait_time = min(wait_max, wait_min * (2**attempt_num))
                                logger.warning(
                                    f"Retrying after {wait_time}s due to: {e}",
                                    extra={
                                        "attempt": attempt_num + 1,
                                        "max_attempts": max_attempts,
                                    },
                                )
                                await asyncio.sleep(wait_time)

                    # This should never be reached, but just in case
                    if last_exception:
                        raise last_exception

                return async_wrapper
            else:

                @functools.wraps(func)
                def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
                    last_exception = None

                    for attempt_num in range(max_attempts):
                        try:
                            return func(*args, **kwargs)
                        except exception_types as e:
                            last_exception = e

                            if attempt_num == max_attempts - 1:
                                # Last attempt, re-raise the exception
                                raise

                            # For sync functions, use simpler retry logic
                            wait_time = min(wait_max, wait_min * (2**attempt_num))
                            logger.warning(
                                f"Retrying after {wait_time}s due to: {e}",
                                extra={"attempt": attempt_num + 1, "max_attempts": max_attempts},
                            )
                            import time

                            time.sleep(wait_time)

                    # This should never be reached, but just in case
                    if last_exception:
                        raise last_exception

                return sync_wrapper

        return decorator

    @retry_on_exception(max_attempts=3, wait_min=2.0, wait_max=30.0, network_aware=True)
    async def create_container(
        self, image_tag: str, container_options: Optional[str] = None
    ) -> str:
        """
        Create and run a new container from the specified image.

        Args:
            image_tag: Tag of the Docker image to run
            container_options: Additional Docker run options (e.g., "-p 8080:80 -e ENV_VAR=value")

        Returns:
            Container ID of the created container

        Raises:
            RuntimeError: If container creation fails
        """
        logger.info(
            "Creating new container",
            ssh_host=self.ssh_host,
            image_tag=image_tag,
            container_name=self.container_name,
            options=container_options,
        )

        try:
            # Step 1: Stop and remove existing container if it exists
            logger.info("Step 1: Stopping and removing existing container if it exists")
            return_code, stdout, stderr = await self._run_ssh_command(
                f"docker stop {self.container_name} || true && docker rm {self.container_name} || true"
            )
            # Don't fail if container doesn't exist

            # Step 2: Create and run new container
            logger.info("Step 2: Creating and running new container")
            docker_run_cmd = f"docker run -dit --name {self.container_name}"

            if container_options:
                docker_run_cmd += f" {container_options}"

            docker_run_cmd += f" {image_tag}"

            return_code, stdout, stderr = await self._run_ssh_command(
                docker_run_cmd, timeout=120.0  # 2 minutes for container start
            )

            if return_code != 0:
                raise RuntimeError(f"Container creation failed: {stderr}")

            container_id = stdout.strip()

            return_code, stdout, stderr = await self._run_ssh_command(f"ls")

            # Step 3: Verify container is running
            logger.info("Step 3: Verifying container is running")
            return_code, stdout, stderr = await self._run_ssh_command(
                f"docker ps --filter name={self.container_name} --format '{{{{.ID}}}} {{{{.Status}}}}'"
            )

            if return_code != 0 or not stdout.strip():
                raise RuntimeError(f"Container verification failed: {stderr}")

            container_status = stdout.strip()
            logger.info(
                "Container created successfully",
                container_id=container_id,
                container_status=container_status,
            )

            return container_id

        except Exception as e:
            logger.error(
                "Failed to create container",
                ssh_host=self.ssh_host,
                container_name=self.container_name,
                image_tag=image_tag,
                error=str(e),
            )
            raise

    @retry_on_exception(max_attempts=3, wait_min=2.0, wait_max=30.0, network_aware=True)
    async def stop_container(self, force: bool = False, timeout: int = 30) -> bool:
        """
        Stop the running container.

        Args:
            force: If True, force stop the container (docker kill)
            timeout: Timeout in seconds for graceful stop before forcing

        Returns:
            True if container was stopped successfully, False if container was not running

        Raises:
            RuntimeError: If stop operation fails
        """
        logger.info(
            "Stopping container",
            ssh_host=self.ssh_host,
            container_name=self.container_name,
            force=force,
            timeout=timeout,
        )

        try:
            # Check if container exists and is running
            return_code, stdout, stderr = await self._run_ssh_command(
                f"docker ps --filter name={self.container_name} --format '{{{{.Names}}}}'"
            )

            if return_code != 0:
                raise RuntimeError(f"Failed to check container status: {stderr}")

            if not stdout.strip():
                logger.info("Container is not running", container_name=self.container_name)
                return False

            # Stop the container
            if force:
                stop_cmd = f"docker kill {self.container_name}"
                logger.info("Force stopping container")
            else:
                stop_cmd = f"docker stop --time {timeout} {self.container_name}"
                logger.info("Gracefully stopping container", timeout=timeout)

            return_code, stdout, stderr = await self._run_ssh_command(
                stop_cmd, timeout=float(timeout + 30)  # Add buffer to SSH timeout
            )

            if return_code != 0:
                raise RuntimeError(f"Container stop failed: {stderr}")

            logger.info("Container stopped successfully", container_name=self.container_name)
            return True

        except Exception as e:
            logger.error(
                "Failed to stop container",
                ssh_host=self.ssh_host,
                container_name=self.container_name,
                error=str(e),
            )
            raise

    @retry_on_exception(max_attempts=3, wait_min=2.0, wait_max=30.0, network_aware=True)
    async def delete_container(self, force: bool = False) -> bool:
        """
        Delete (remove) the container.

        Args:
            force: If True, force remove the container even if it's running

        Returns:
            True if container was deleted successfully, False if container didn't exist

        Raises:
            RuntimeError: If delete operation fails
        """
        logger.info(
            "Deleting container",
            ssh_host=self.ssh_host,
            container_name=self.container_name,
            force=force,
        )

        try:
            # Check if container exists (running or stopped)
            return_code, stdout, stderr = await self._run_ssh_command(
                f"docker ps -a --filter name={self.container_name} --format '{{{{.Names}}}}'"
            )

            if return_code != 0:
                raise RuntimeError(f"Failed to check container existence: {stderr}")

            if not stdout.strip():
                logger.info("Container does not exist", container_name=self.container_name)
                return False

            # Remove the container
            if force:
                remove_cmd = f"docker rm -f {self.container_name}"
                logger.info("Force removing container")
            else:
                remove_cmd = f"docker rm {self.container_name}"
                logger.info("Removing container")

            return_code, stdout, stderr = await self._run_ssh_command(remove_cmd)

            if return_code != 0:
                raise RuntimeError(f"Container removal failed: {stderr}")

            logger.info("Container deleted successfully", container_name=self.container_name)
            return True

        except Exception as e:
            logger.error(
                "Failed to delete container",
                ssh_host=self.ssh_host,
                container_name=self.container_name,
                error=str(e),
            )
            raise

    async def check_container_health(self) -> bool:
        """Check if container is running and healthy"""
        cmd = f"docker inspect --format='{{{{.State.Status}}}} {self.config.container_name}"
        return_code, stdout, _ = await self._run_ssh_command(cmd)
        return return_code == 0 and stdout.strip() == "running"
