import asyncio
import sys
import threading
from concurrent.futures import Future
from typing import Optional, Dict, List, Any
from collections.abc import Coroutine
import logging
import asyncio
from concurrent.futures import Future, TimeoutError as FutureTimeoutError
from typing import Optional, Dict, List, Any, Coroutine

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(name)s - %(message)s"
)
logger = logging.getLogger(__name__)

DEFAULT_REQUEST_TIMEOUT_SECONDS = 10.0
DEFAULT_MAX_RETRIES = 2
DEFAULT_RETRY_DELAY_SECONDS = 3.0
DEFAULT_RUN_ASYNC_OVERALL_TIMEOUT = (DEFAULT_MAX_RETRIES + 1) * (
    DEFAULT_REQUEST_TIMEOUT_SECONDS + DEFAULT_RETRY_DELAY_SECONDS
) + 5.0  # e.g., (2+1)*(5+3) + 5 = 24 + 5 = 29 seconds


try:
    import mcp.types as types
    from mcp.client.session import ClientSession
    from mcp.client.sse import sse_client
    from mcp.client.streamable_http import streamablehttp_client
except ImportError as e:
    print(f"❌ Missing MCP dependencies. Install with: pip install mcp")
    sys.exit(1)

try:
    import httpx
except ImportError as e:
    print(f"❌ Missing httpx dependency. Install with: pip install httpx")
    sys.exit(1)


class MCPToolsLister:
    def __init__(self, server_url, headers=None):
        if not server_url:
            raise ValueError("server_url is required")
        self.server_url = server_url
        self.headers = headers or {}
        if "/mcp" in server_url:
            self.connection_type = "streamable_http"
        else:
            self.connection_type = "sse"
        self._session = None
        self._context = None

    async def __aenter__(self):
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        logger.debug(f"MCPToolsLister exiting context. exc_type: {exc_type}")
        await self.close()
        logger.debug("MCPToolsLister context exited.")

    async def connect(self):
        logger.info(f"🔗 Connecting via {self.connection_type.upper()} to {self.server_url}...")
        if self.connection_type == "sse":
            self._context = sse_client(self.server_url, headers=self.headers)
        else:
            self._context = streamablehttp_client(self.server_url, headers=self.headers)
        try:
            result = await self._context.__aenter__()
        except Exception as e:
            logger.error(
                f"❌ Failed to enter context for {self.connection_type} client: {e}", exc_info=True
            )
            raise
        if isinstance(result, tuple):
            self._streams = (result[0], result[1])
        else:
            self._streams = (result, result)
        self._session = ClientSession(self._streams[0], self._streams[1])
        try:
            await self._session.__aenter__()
            await self._session.initialize()
        except Exception as e:
            logger.error(f"❌ Failed during MCP session initialization: {e}", exc_info=True)
            if self._context:
                try:
                    await self._context.__aexit__(type(e), e, e.__traceback__)
                except Exception as cleanup_exc:
                    logger.warning(
                        f"Error during context cleanup after session init failure: {cleanup_exc}"
                    )
            raise
        logger.info("✅ Connected successfully!")

    async def list_tools(self):
        logger.info("📋 Listing tools...")
        try:
            tools_result = await self._session.list_tools()
            logger.info(f"Raw tools_result from session: {tools_result}")
            return tools_result
        except httpx.HTTPStatusError as e:
            logger.error(
                f"❌ HTTP Error listing tools: {e.response.status_code} - {e.response.text}",
                exc_info=True,
            )
            raise
        except httpx.RequestError as e:
            logger.error(f"❌ Network Error listing tools: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"❌ Failed to list tools with an unexpected error: {e}", exc_info=True)
            raise

    async def close(self):
        logger.debug("Attempting to close MCPToolsLister session and context...")
        try:
            if self._session:
                logger.debug("Closing MCP session...")
                await self._session.__aexit__(None, None, None)
                logger.debug("MCP session closed.")
            if self._context:
                logger.debug(f"Closing {self.connection_type} context...")
                await self._context.__aexit__(None, None, None)
                logger.debug(f"{self.connection_type} context closed.")
        except Exception as e:
            logger.warning(f"Cleanup error during close: {e}", exc_info=True)
        logger.debug("MCPToolsLister close method finished.")


async def _get_tools_async_with_retry(
    server_url: str,
    timeout_seconds: float,
    max_retries: int,
    retry_delay_seconds: float,
) -> Optional[Any]:
    last_exception = None
    for attempt in range(max_retries + 1):
        try:
            logger.info(f"Attempt {attempt + 1}/{max_retries + 1} to get tools from {server_url}")

            async def _fetch_operation():
                async with MCPToolsLister(server_url) as client:  # This can raise init errors
                    tools = await client.list_tools()  # This can raise op errors
                    logger.info(f"[DEBUG] _fetch_operation is about to return tools: {tools}")
                    return tools

            tools_result = await asyncio.wait_for(_fetch_operation(), timeout=timeout_seconds)
            logger.info(f"Successfully retrieved tools from {server_url} on attempt {attempt + 1}")
            return tools_result
        except asyncio.TimeoutError:
            last_exception = asyncio.TimeoutError(
                f"Timeout after {timeout_seconds}s on attempt {attempt + 1} for {server_url}"
            )
            logger.warning(str(last_exception))
        except ValueError as ve:  # Raised by MCPToolsLister for bad URL etc.
            logger.error(f"Invalid input/config for MCP server '{server_url}': {ve}. Not retrying.")
            # This error is not recoverable by retrying, propagate it or return None.
            # Returning None here means get_mcp_tools_async will also return None.
            return None
        except Exception as e:  # Catches McpError, httpx.RequestError, etc.
            last_exception = e
            logger.error(
                f"Error getting tools from MCP server {server_url} on attempt {attempt + 1}: {e}",
                exc_info=True,
            )

        if attempt < max_retries:
            logger.info(f"Retrying in {retry_delay_seconds} seconds...")
            await asyncio.sleep(retry_delay_seconds)
        else:
            logger.error(
                f"Failed to get tools from {server_url} after {max_retries + 1} attempts. "
                f"Last error: {last_exception}"
            )
            if last_exception:
                # This exception will be caught by get_mcp_tools_async
                raise last_exception
            return None  # Fallback, should be covered by last_exception
    return None  # Should only be reached if max_retries is < 0 (not typical)


async def get_mcp_tools_async(
    server_url: str,
    timeout_seconds: float = DEFAULT_REQUEST_TIMEOUT_SECONDS,
    max_retries: int = DEFAULT_MAX_RETRIES,
    retry_delay_seconds: float = DEFAULT_RETRY_DELAY_SECONDS,
) -> Optional[Any]:
    try:
        return await _get_tools_async_with_retry(
            server_url, timeout_seconds, max_retries, retry_delay_seconds
        )
    except Exception as e:
        # Catches exceptions re-raised by _get_tools_async_with_retry after all retries.
        # Also catches direct ValueError if it were re-raised instead of returning None.
        logger.error(
            f"Failed to get MCP tools for {server_url} after all retries or due to critical error: {e}",
            exc_info=True,
        )
        return None  # Ensure None is returned on any ultimate failure


# Your tools_to_json_response or tools_to_json_response_http function
def tools_to_json_response_http(
    tools_result: Any,
) -> Optional[dict]:  # Assuming this is the one used
    if tools_result is None:
        return None

    tools_list = []
    # Adapt this based on the actual structure of tools_result from your MCP client
    actual_tools_data = getattr(tools_result, "tools", None)
    if actual_tools_data is None:
        if isinstance(tools_result, list):
            actual_tools_data = tools_result
        elif hasattr(tools_result, "result") and isinstance(
            tools_result.result, list
        ):  # common pattern for result objects
            actual_tools_data = tools_result.result
        else:
            actual_tools_data = []

    for tool_obj in actual_tools_data:
        tool_dict = {
            "name": getattr(tool_obj, "name", "Unknown Name"),
            "description": getattr(tool_obj, "description", "No Description"),
            "input_schema": getattr(tool_obj, "inputSchema", None),  # Check actual attribute names
            "annotations": getattr(tool_obj, "annotations", None),
        }
        tools_list.append(tool_dict)

    # Check if tools_result has meta and nextCursor, adapt as needed
    meta_data = getattr(tools_result, "meta", None)
    next_cursor_data = getattr(tools_result, "nextCursor", None)

    return {
        "meta": meta_data,
        "nextCursor": next_cursor_data,
        "tools": tools_list,
    }



def run_async_from_sync(
    coro: Coroutine, overall_timeout: Optional[float] = DEFAULT_RUN_ASYNC_OVERALL_TIMEOUT
) -> Any:
    future_result = Future()
    thread_name = f"AsyncRunner-{id(coro)}-{threading.get_ident()}"

    def thread_target():
        loop_identity = f"Loop for {thread_name}"
        logger.debug(f"{loop_identity}: Starting event loop.")
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        except Exception as e_loop_init:
            logger.error(
                f"{loop_identity}: Failed to create or set new event loop: {e_loop_init}",
                exc_info=True,
            )
            if not future_result.done():
                future_result.set_exception(e_loop_init)
            return

        main_task = None
        try:
            logger.debug(f"{loop_identity}: Creating task for coroutine.")
            main_task = loop.create_task(coro)
            logger.debug(f"{loop_identity}: Running coroutine until complete.")
            result = loop.run_until_complete(main_task)
            logger.debug(f"{loop_identity}: Coroutine completed. Result type: {type(result)}")
            if not future_result.done():
                future_result.set_result(result)
        except asyncio.CancelledError as e_cancel:
            # This is the path hit based on your logs
            logger.warning(f"{loop_identity}: Coroutine was cancelled. Exception: {e_cancel}")
            if not future_result.done():
                future_result.set_exception(e_cancel)
        except Exception as e_coro:
            logger.error(f"{loop_identity}: Coroutine raised an exception: {e_coro}", exc_info=True)
            if not future_result.done():
                future_result.set_exception(e_coro)
        finally:
            logger.debug(
                f"{loop_identity}: Entering main finally block. Future done: {future_result.done()}"
            )
            # Ensure future is resolved if it somehow wasn't (should be redundant)
            if not future_result.done():
                logger.error(
                    f"{loop_identity}: Future was not resolved by try/except; setting RuntimeError."
                )
                future_result.set_exception(
                    RuntimeError("Async operation ended without resolving future.")
                )

            logger.debug(f"{loop_identity}: Starting event loop cleanup.")
            try:
                if loop.is_running():
                    logger.warning(
                        f"{loop_identity}: Loop is still running during cleanup, attempting shutdown."
                    )

                # Graceful shutdown of async generators
                logger.debug(f"{loop_identity}: Shutting down async generators.")
                loop.run_until_complete(loop.shutdown_asyncgens())
                logger.debug(f"{loop_identity}: Async generators shut down.")

                # Cancel all remaining tasks
                logger.debug(f"{loop_identity}: Gathering remaining tasks for cancellation.")
                remaining_tasks = [t for t in asyncio.all_tasks(loop) if not t.done()]
                if remaining_tasks:
                    logger.debug(
                        f"{loop_identity}: Cancelling {len(remaining_tasks)} remaining tasks."
                    )
                    for task in remaining_tasks:
                        task.cancel()
                    logger.debug(f"{loop_identity}: Gathering cancelled tasks.")
                    loop.run_until_complete(
                        asyncio.gather(*remaining_tasks, return_exceptions=True)
                    )
                    logger.debug(f"{loop_identity}: Remaining tasks gathered.")
                else:
                    logger.debug(f"{loop_identity}: No remaining tasks to cancel.")

            except Exception as e_cleanup:
                logger.error(
                    f"{loop_identity}: Error during event loop cleanup: {e_cleanup}", exc_info=True
                )
            finally:
                if not loop.is_closed():
                    logger.debug(f"{loop_identity}: Closing event loop.")
                    loop.close()
                    logger.debug(f"{loop_identity}: Event loop closed.")
                # Detach the loop from the current context
                asyncio.set_event_loop(None)
                logger.debug(f"{loop_identity}: Thread target finished.")

    thread = threading.Thread(target=thread_target, name=thread_name, daemon=True)
    logger.debug(f"Main thread: Starting worker thread {thread_name}")
    thread.start()

    logger.debug(
        f"Main thread: Waiting for future from {thread_name} with timeout {overall_timeout}s."
    )
    try:
        # This call blocks the main (gRPC) thread.
        res = future_result.result(timeout=overall_timeout)
        logger.debug(f"Main thread: Future from {thread_name} completed successfully.")
        return res
    except FutureTimeoutError:  # This is concurrent.futures.TimeoutError
        logger.error(f"Main thread: Future from {thread_name} timed out after {overall_timeout}s.")
        if thread.is_alive():
            logger.error(
                f"Main thread: Worker thread {thread_name} is STILL ALIVE after timeout. This indicates a hang in the worker."
            )
            # You might consider more drastic measures here if thread leakage is a major concern,
            # but daemon=True means it won't prevent process exit.
        else:
            logger.warning(
                f"Main thread: Worker thread {thread_name} is not alive after timeout (may have crashed or finished late)."
            )
        raise asyncio.TimeoutError(
            f"Tool fetching operation exceeded overall timeout of {overall_timeout}s for {thread_name}."
        )
    except asyncio.CancelledError as e_main_cancel:  # Exception set by worker thread
        logger.warning(
            f"Main thread: Future from {thread_name} resulted in CancelledError: {e_main_cancel}"
        )
        raise  # Re-raise to be handled by createMCP
    except Exception as e_main_other:  # Exception set by worker thread
        logger.error(
            f"Main thread: Future from {thread_name} resulted in other error: {e_main_other}",
            exc_info=True,
        )
        raise  # Re-raise to be handled by createMCP
