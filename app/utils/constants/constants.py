from enum import Enum


class McpVisibility(str, Enum):
    PRIVATE = "private"
    PUBLIC = "public"


class McpStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"


class McpOwnerType(str, Enum):
    USER = "user"
    ENTERPRISE = "enterprise"
    PLATFORM = "platform"


class McpCategory(str, Enum):
    GENERAL = "general"
    SALES = "sales"
    MARKETING = "marketing"
    ENGINEERING = "engineering"
    FINANCE = "finance"


class UrlType(str, Enum):
    SSE = "sse"
    HTTP = "http"
    STDIO = "stdio"
    
class DeploymentStatus(str, Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    SUCCESS = "success"
    FAILED = "failed"


class MarketplaceItemSortEnum(str, Enum):
    NEWEST = "newest"
    OLDEST = "oldest"
    MOST_POPULAR = "most_popular"
    HIGHEST_RATED = "highest_rated"


class EnvCredentialStatus(str, Enum):
    PENDING_INPUT = "pending_input"
    PROVIDED = "provided"
    NOT_REQUIRED = "not_required"

class McpComponentCategory(str, Enum):
    NOTIFICATIONS_ALERTS = "notifications_alerts"
    COMMUNICATION = "communication"
    SOCIAL_MEDIA = "social_media"
    DATABASE = "database"
    CLOUD_STORAGE = "cloud_storage"
    DEVOPS_SYSTEM = "devops_system"
    FILE_HANDLING = "file_handling"


class ContainerStatus(str, Enum):
    RUNNING = "running"
    STOPPED = "stopped"
    DELETED = "deleted"